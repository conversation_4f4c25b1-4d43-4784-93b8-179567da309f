import qrcode
from PIL import Image

url = "http://8.148.223.122:5000/"
logo_path = "/home/<USER>/upload/favicon.png"

# 进一步提高box_size以生成更高分辨率的二维码
qr = qrcode.QRCode(
    version=1,
    error_correction=qrcode.constants.ERROR_CORRECT_H, # 使用高容错率
    box_size=40, # 进一步增加box_size以提高分辨率
    border=4,
)
qr.add_data(url)
qr.make(fit=True)

img = qr.make_image(fill_color="black", back_color="white").convert("RGBA")

logo = Image.open(logo_path).convert("RGBA")

# 计算logo尺寸，使其适应二维码中心区域，并留出一定边距
qr_width, qr_height = img.size

# 调整logo大小，使其大约是二维码的1/4到1/3，并保持高宽比
logo_max_size = min(qr_width, qr_height) // 4 # 调整为二维码的1/4大小
logo.thumbnail((logo_max_size, logo_max_size), Image.LANCZOS)

# 计算logo粘贴位置，使其居中
logo_width, logo_height = logo.size
pos = ((qr_width - logo_width) // 2, (qr_height - logo_height) // 2)

# 在二维码上创建一个空白区域用于放置logo
blank_area = Image.new("RGBA", (logo_width, logo_height), (255, 255, 255, 255))
img.paste(blank_area, pos)

# 将logo粘贴到空白区域
img.paste(logo, pos, logo)

img.save("/home/<USER>/qrcode_with_higher_res_clean_logo.png")

